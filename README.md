# CSV ChatGPT 处理脚本

这个Python脚本可以读取CSV文件，将图片地址和prompt传递给ChatGPT API，并将响应结果添加到CSV的新列中。

## 功能特点

- 支持处理包含图片路径和文本prompt的CSV文件
- 使用OpenAI的GPT-4 Vision模型处理图片和文本
- 自动将ChatGPT响应添加到新的列中
- 支持断点续传（如果程序中断，可以从上次停止的地方继续）
- 支持批量处理和进度保存
- 包含错误处理和日志记录

## 安装依赖

```bash
pip install -r requirements.txt
```

## 设置API密钥

在运行脚本之前，需要设置OpenAI API密钥：

```bash
export OPENAI_API_KEY='your-api-key-here'
```
或者在运行时通过参数传递：

```bash
python process_csv_with_chatgpt.py input.csv output.csv --api-key your-api-key-here
```

## 使用方法

### 方法1：快速测试（推荐新手使用）

```bash
# 快速测试第1行
python quick_test.py

# 快速测试前5行
python quick_test.py 5

# 快速测试第1行（明确指定）
python quick_test.py first
```

### 方法2：交互式测试

```bash
# 运行交互式脚本，可以选择测试任意行或范围
python run_example.py
```

这个脚本会显示CSV预览，然后让您选择：
- 测试单行（指定行号）
- 测试前N行
- 测试指定范围
- 查看更多CSV预览

### 方法3：使用命令行参数

```bash
# 基本使用
python process_csv_with_chatgpt.py new_csv/content_CogAgent.csv output.csv

# 指定延迟和处理范围
python process_csv_with_chatgpt.py new_csv/content_CogAgent.csv output.csv --delay 2.0 --start-row 0 --end-row 10

# 查看所有参数
python process_csv_with_chatgpt.py --help
```

## 参数说明

- `input_file`: 输入CSV文件路径
- `output_file`: 输出CSV文件路径
- `--api-key`: OpenAI API密钥（可选，也可通过环境变量设置）
- `--delay`: API调用之间的延迟时间（秒），默认1.0秒
- `--start-row`: 开始处理的行号（从0开始），默认0
- `--end-row`: 结束处理的行号（不包含），默认处理到最后
- `--model`: 使用的模型，默认'gpt-4-vision-preview'

## CSV文件格式要求

输入的CSV文件需要包含以下列：

- `Image Path`: 图片文件的路径
- `Content of P*`: 要发送给ChatGPT的prompt文本

脚本会自动添加一个新列 `ChatGPT Response` 来存储API响应。

## 示例CSV格式

```csv
Category,Image Path,Content of P*
Adversarial Suffix,/path/to/image1.png,"请分析这张图片..."
Health Consultation,/path/to/image2.jpg,"请描述图片中的内容..."
```

## 注意事项

1. **API费用**: 使用GPT-4 Vision模型会产生费用，请注意控制使用量
2. **API限制**: OpenAI有API调用频率限制，脚本包含延迟机制来避免超限
3. **图片格式**: 支持常见的图片格式（JPG, PNG等）
4. **文件路径**: 确保图片文件路径正确且文件存在
5. **断点续传**: 如果程序中断，重新运行时会跳过已处理的行

## 错误处理

脚本包含完善的错误处理机制：

- 图片文件不存在时会记录错误信息
- API调用失败时会记录错误并继续处理下一行
- 每10行自动保存进度，避免数据丢失

## 日志输出

脚本会输出详细的处理日志，包括：

- 当前处理的行号和文件名
- API调用状态
- 错误信息
- 进度保存信息

## 批处理建议

对于大量数据的处理：

1. 先用小范围测试（如前5行）
2. 设置合适的延迟时间避免API限制
3. 定期检查输出文件确保处理正常
4. 可以分批处理，使用 `--start-row` 和 `--end-row` 参数

## 示例运行

```bash
# 设置API密钥
export OPENAI_API_KEY='sk-your-api-key-here'

# 快速测试第1行（最简单的开始方式）
python quick_test.py

# 快速测试前5行
python quick_test.py 5

# 交互式选择测试范围
python run_example.py

# 使用命令行测试前5行
python process_csv_with_chatgpt.py new_csv/content_CogAgent.csv test_output.csv --delay 2.0 --end-row 5

# 处理全部数据
python process_csv_with_chatgpt.py new_csv/content_CogAgent.csv full_output.csv --delay 1.5
```

## 推荐的测试流程

1. **首次使用**：运行 `python quick_test.py` 测试第1行
2. **确认正常**：运行 `python quick_test.py 5` 测试前5行
3. **大规模处理**：使用 `python run_example.py` 或命令行参数处理更多数据
